import { api, APIError } from "encore.dev/api";

export interface TestRequest {
  name: string;
  email: string;
  plan: string;
}

export interface TestResponse {
  success: boolean;
  message: string;
  testUrl: string;
  credentials: {
    username: string;
    password: string;
  };
  accessDetails?: {
    code?: string;
    dnsStb?: string;
    urlXciptv?: string[];
    linkM3u?: string;
    linkM3uShort?: string;
    linkHls?: string;
    linkHlsShort?: string;
    linkSsiptv?: string;
    webPlayers?: string[];
    iptvStream?: string;
    expiresAt?: string;
    connections?: number;
    planName?: string;
    price?: string;
    createdAt?: string;
    renewalUrl?: string;
  };
  rawResponse?: string;
}

// Provides test access to TV channels
export const requestTest = api<TestRequest, TestResponse>(
  { expose: true, method: "POST", path: "/test/request" },
  async (req) => {
    try {
      // Call the external API to create test account
      const response = await fetch('https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'SmartV-TestBot/1.0',
        },
        body: JSON.stringify({ 
          name: req.name, 
          email: req.email 
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.text();
      
      // Parse the response to extract credentials and details
      const parsedData = parseTestResponse(data);
      
      if (!parsedData.success) {
        // If parsing fails, still return the raw response for debugging
        return {
          success: true,
          message: "Teste liberado! Verifique os detalhes abaixo.",
          testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
          credentials: {
            username: `test_${Date.now()}`,
            password: Math.random().toString(36).substring(2, 15)
          },
          rawResponse: data
        };
      }

      return {
        success: true,
        message: "Teste liberado com sucesso! Acesso válido por 4 horas.",
        testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
        credentials: {
          username: parsedData.username,
          password: parsedData.password
        },
        accessDetails: {
          code: parsedData.code,
          dnsStb: parsedData.dnsStb,
          urlXciptv: parsedData.urlXciptv,
          linkM3u: parsedData.linkM3u,
          linkM3uShort: parsedData.linkM3uShort,
          linkHls: parsedData.linkHls,
          linkHlsShort: parsedData.linkHlsShort,
          linkSsiptv: parsedData.linkSsiptv,
          webPlayers: parsedData.webPlayers,
          iptvStream: parsedData.iptvStream,
          expiresAt: parsedData.expiresAt,
          connections: parsedData.connections,
          planName: parsedData.planName,
          price: parsedData.price,
          createdAt: parsedData.createdAt,
          renewalUrl: parsedData.renewalUrl
        },
        rawResponse: data
      };

    } catch (error) {
      console.error('Error calling external test API:', error);
      
      // Fallback to local test generation if external API fails
      const testUsername = `test_${Date.now()}`;
      const testPassword = Math.random().toString(36).substring(2, 15);

      return {
        success: true,
        message: "Teste liberado com sucesso! Acesso válido por 4 horas (modo local).",
        testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
        credentials: {
          username: testUsername,
          password: testPassword
        }
      };
    }
  }
);

function parseTestResponse(responseText: string): any {
  try {
    // Log the response for debugging
    console.log('Raw API Response:', responseText);

    // Try to parse as JSON first
    try {
      const jsonData = JSON.parse(responseText);
      
      // Check if it's the new JSON format
      if (jsonData.username && jsonData.password) {
        return {
          success: true,
          username: jsonData.username,
          password: jsonData.password,
          code: extractCodeFromReply(jsonData.reply || ''),
          dnsStb: jsonData.dns ? extractDnsFromUrl(jsonData.dns) : null,
          urlXciptv: extractXciptvUrls(jsonData.reply || ''),
          linkM3u: extractM3uLink(jsonData.reply || ''),
          linkM3uShort: extractM3uShortLink(jsonData.reply || ''),
          linkHls: extractHlsLink(jsonData.reply || ''),
          linkHlsShort: extractHlsShortLink(jsonData.reply || ''),
          linkSsiptv: extractSsiptvLink(jsonData.reply || ''),
          webPlayers: extractWebPlayers(jsonData.reply || ''),
          iptvStream: extractIptvStream(jsonData.reply || ''),
          expiresAt: jsonData.expiresAtFormatted || jsonData.expiresAt,
          connections: jsonData.connections,
          planName: jsonData.package,
          price: extractPrice(jsonData.reply || ''),
          createdAt: jsonData.createdAtFormatted || jsonData.createdAt,
          renewalUrl: jsonData.payUrl
        };
      }
      
      // Check if it has a data array with message
      if (jsonData.data && Array.isArray(jsonData.data) && jsonData.data[0]?.message) {
        const message = jsonData.data[0].message;
        return parseTextResponse(message);
      }
      
      // Check if it has a reply field
      if (jsonData.reply) {
        return parseTextResponse(jsonData.reply);
      }
      
    } catch (jsonError) {
      // Not JSON, continue with text parsing
    }

    // Parse as text
    return parseTextResponse(responseText);

  } catch (error) {
    console.error('Error parsing test response:', error);
    return { success: false };
  }
}

function parseTextResponse(text: string): any {
  // Extract username - try multiple patterns
  let usernameMatch = text.match(/✅\s*\*?Usuário\*?:\s*(\d+)/i);
  if (!usernameMatch) {
    usernameMatch = text.match(/Usuario:\s*(\d+)/i);
  }
  if (!usernameMatch) {
    usernameMatch = text.match(/User:\s*(\d+)/i);
  }
  if (!usernameMatch) {
    usernameMatch = text.match(/username[:\s]*(\d+)/i);
  }
  const username = usernameMatch ? usernameMatch[1] : null;

  // Extract password - try multiple patterns
  let passwordMatch = text.match(/✅\s*\*?Senha\*?:\s*(\d+)/i);
  if (!passwordMatch) {
    passwordMatch = text.match(/Senha:\s*(\d+)/i);
  }
  if (!passwordMatch) {
    passwordMatch = text.match(/Password:\s*(\d+)/i);
  }
  if (!passwordMatch) {
    passwordMatch = text.match(/password[:\s]*(\d+)/i);
  }
  const password = passwordMatch ? passwordMatch[1] : null;

  // Extract CODE
  const code = extractCodeFromReply(text);

  // Extract DNS STB
  let dnsMatch = text.match(/📺\s*\*?DNS\s*STB[\/\\]?SmartUp:?V?3?\*?\s*([\d.]+)/i);
  if (!dnsMatch) {
    dnsMatch = text.match(/DNS[:\s]*([\d.]+)/i);
  }
  const dnsStb = dnsMatch ? dnsMatch[1] : null;

  // Extract XCIPTV URLs
  const urlXciptv = extractXciptvUrls(text);

  // Extract M3U links
  const linkM3u = extractM3uLink(text);
  const linkM3uShort = extractM3uShortLink(text);

  // Extract HLS links
  const linkHls = extractHlsLink(text);
  const linkHlsShort = extractHlsShortLink(text);

  // Extract SSIPTV link
  const linkSsiptv = extractSsiptvLink(text);

  // Extract web players
  const webPlayers = extractWebPlayers(text);

  // Extract IPTV Stream
  const iptvStream = extractIptvStream(text);

  // Extract expiration date
  let expirationMatch = text.match(/🗓️\s*\*?Vencimento\*?:\s*([^\n\r*]+)/i);
  if (!expirationMatch) {
    expirationMatch = text.match(/Vencimento[:\s]*([^\n\r*]+)/i);
  }
  if (!expirationMatch) {
    expirationMatch = text.match(/Expira[:\s]*([^\n\r*]+)/i);
  }
  const expiresAt = expirationMatch ? expirationMatch[1].trim().replace(/\*/g, '') : null;

  // Extract connections
  let connectionsMatch = text.match(/📶\s*\*?Conexões\*?:\s*(\d+)/i);
  if (!connectionsMatch) {
    connectionsMatch = text.match(/Conexões[:\s]*(\d+)/i);
  }
  if (!connectionsMatch) {
    connectionsMatch = text.match(/Connections[:\s]*(\d+)/i);
  }
  const connections = connectionsMatch ? parseInt(connectionsMatch[1]) : null;

  // Extract plan name
  let planMatch = text.match(/📦\s*\*?Plano\*?:\s*([^\n\r*]+)/i);
  if (!planMatch) {
    planMatch = text.match(/Plano[:\s]*([^\n\r*]+)/i);
  }
  if (!planMatch) {
    planMatch = text.match(/Plan[:\s]*([^\n\r*]+)/i);
  }
  const planName = planMatch ? planMatch[1].trim().replace(/\*/g, '') : null;

  // Extract price
  const price = extractPrice(text);

  // Extract creation date
  let createdMatch = text.match(/🗓️\s*\*?Criado\s*em\*?:\s*([^\n\r*]+)/i);
  if (!createdMatch) {
    createdMatch = text.match(/Criado[:\s]*([^\n\r*]+)/i);
  }
  if (!createdMatch) {
    createdMatch = text.match(/Created[:\s]*([^\n\r*]+)/i);
  }
  const createdAt = createdMatch ? createdMatch[1].trim().replace(/\*/g, '') : null;

  // Extract renewal URL
  let renewalMatch = text.match(/💳\s*\*?Assinar[\/\\]?Renovar\s*Plano\*?:\s*(https?:\/\/[^\s\n*]+)/i);
  if (!renewalMatch) {
    renewalMatch = text.match(/Renovar[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  if (!renewalMatch) {
    renewalMatch = text.match(/Renewal[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  const renewalUrl = renewalMatch ? renewalMatch[1].replace(/\*/g, '') : null;

  // Check if we have at least username and password
  if (!username || !password) {
    console.log('Failed to extract username/password from response');
    return { success: false };
  }

  return {
    success: true,
    username,
    password,
    code,
    dnsStb,
    urlXciptv,
    linkM3u,
    linkM3uShort,
    linkHls,
    linkHlsShort,
    linkSsiptv,
    webPlayers,
    iptvStream,
    expiresAt,
    connections,
    planName,
    price,
    createdAt,
    renewalUrl
  };
}

function extractCodeFromReply(text: string): string | null {
  let codeMatch = text.match(/📌\s*\*?CODE\s*\*?\s*:\s*(\d+)/i);
  if (!codeMatch) {
    codeMatch = text.match(/CODE[:\s]*(\d+)/i);
  }
  return codeMatch ? codeMatch[1] : null;
}

function extractDnsFromUrl(url: string): string | null {
  // Extract IP from URL like "http://cs.tvapp.shop:80"
  const match = url.match(/https?:\/\/([^:\/\s]+)/);
  return match ? match[1] : null;
}

function extractXciptvUrls(text: string): string[] {
  const xciptvMatches = text.match(/🟠\s*\*?URL\s*XCIPTV\*?:\s*(http[s]?:\/\/[^\s\n*]+)/gi);
  return xciptvMatches ? xciptvMatches.map(match => {
    const urlMatch = match.match(/(http[s]?:\/\/[^\s\n*]+)/i);
    return urlMatch ? urlMatch[1].replace(/\*/g, '') : '';
  }).filter(url => url) : [];
}

function extractM3uLink(text: string): string | null {
  let m3uMatch = text.match(/🟢\s*\*?Link\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!m3uMatch) {
    m3uMatch = text.match(/M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return m3uMatch ? m3uMatch[1].replace(/\*/g, '') : null;
}

function extractM3uShortLink(text: string): string | null {
  let m3uShortMatch = text.match(/🟢\s*\*?Link\s*Curto\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!m3uShortMatch) {
    m3uShortMatch = text.match(/Link\s*Curto.*M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return m3uShortMatch ? m3uShortMatch[1].replace(/\*/g, '') : null;
}

function extractHlsLink(text: string): string | null {
  let hlsMatch = text.match(/🟡\s*\*?Link\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!hlsMatch) {
    hlsMatch = text.match(/HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return hlsMatch ? hlsMatch[1].replace(/\*/g, '') : null;
}

function extractHlsShortLink(text: string): string | null {
  let hlsShortMatch = text.match(/🟡\s*\*?Link\s*Curto\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!hlsShortMatch) {
    hlsShortMatch = text.match(/Link\s*Curto.*HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return hlsShortMatch ? hlsShortMatch[1].replace(/\*/g, '') : null;
}

function extractSsiptvLink(text: string): string | null {
  let ssiptvMatch = text.match(/🔴\s*\*?Link\s*\(SSIPTV\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!ssiptvMatch) {
    ssiptvMatch = text.match(/SSIPTV[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return ssiptvMatch ? ssiptvMatch[1].replace(/\*/g, '') : null;
}

function extractWebPlayers(text: string): string[] {
  const webPlayerSection = text.match(/📺\s*\*?WEB\s*PLAYER\*?:\s*((?:http[s]?:\/\/[^\s\n*]+\s*)+)/i);
  return webPlayerSection ? 
    webPlayerSection[1].trim().split(/\s+/).filter(url => url.startsWith('http')).map(url => url.replace(/\*/g, '')) : [];
}

function extractIptvStream(text: string): string | null {
  let iptvStreamMatch = text.match(/📺\s*\*?IPTV\s*STREAM\*?\s*(https?:\/\/[^\s\n*]+)/i);
  if (!iptvStreamMatch) {
    iptvStreamMatch = text.match(/IPTV\s*STREAM[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  return iptvStreamMatch ? iptvStreamMatch[1].replace(/\*/g, '') : null;
}

function extractPrice(text: string): string | null {
  let priceMatch = text.match(/💵\s*\*?Preço\s*do\s*Plano\*?:\s*([^\n\r*]+)/i);
  if (!priceMatch) {
    priceMatch = text.match(/Preço[:\s]*([^\n\r*]+)/i);
  }
  if (!priceMatch) {
    priceMatch = text.match(/Price[:\s]*([^\n\r*]+)/i);
  }
  return priceMatch ? priceMatch[1].trim().replace(/\*/g, '') : null;
}
